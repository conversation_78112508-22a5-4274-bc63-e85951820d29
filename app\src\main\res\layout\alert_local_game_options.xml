<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="40dp"
        android:paddingTop="40dp"
        android:paddingBottom="20dp"
        android:gravity="center"
        android:background="@drawable/alert_dialogue_background_3"
        android:layout_marginTop="20dp"
        >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"


            >
            <LinearLayout
                android:id="@+id/alertdialogueplaylocalbox1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                >
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="30dp">

                    <View
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_centerHorizontal="true"
                        android:layout_alignParentBottom="true"
                        android:background="@drawable/checl_box"
                        android:backgroundTint="@color/red1"
                        />
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/alertdialogueplaylocalcheck1"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/check_icon"
                        android:layout_marginBottom="5dp"
                        android:layout_marginLeft="8dp"
                        app:tint="@color/white"
                        />
                </RelativeLayout>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:textColor="@color/white"
                    android:gravity="center"
                    android:text="1p"
                    android:fontFamily="@font/oswald"
                    android:textSize="20dp"
                    android:layout_marginTop="5dp"
                    />
            </LinearLayout>
            <LinearLayout
                android:id="@+id/alertdialogueplaylocalbox2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                >
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="30dp">

                    <View
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_centerHorizontal="true"
                        android:layout_alignParentBottom="true"
                        android:background="@drawable/checl_box"
                        android:backgroundTint="@color/yellow1"
                        />
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/alertdialogueplaylocalcheck2"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/check_icon"
                        android:layout_marginBottom="5dp"
                        android:layout_marginLeft="8dp"
                        app:tint="@color/white"
                        />
                </RelativeLayout>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:textColor="@color/white"
                    android:gravity="center"
                    android:text="2p"
                    android:fontFamily="@font/oswald"
                    android:textSize="20dp"
                    android:layout_marginTop="5dp"
                    />
            </LinearLayout>
            <LinearLayout
                android:id="@+id/alertdialogueplaylocalbox3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                >
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="30dp">

                    <View
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_centerHorizontal="true"
                        android:layout_alignParentBottom="true"
                        android:background="@drawable/checl_box"
                        android:backgroundTint="@color/blue1"
                        />
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/alertdialogueplaylocalcheck3"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/check_icon"
                        android:layout_marginBottom="5dp"
                        android:layout_marginLeft="8dp"
                        app:tint="@color/white"
                        />
                </RelativeLayout>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:textColor="@color/white"
                    android:gravity="center"
                    android:text="3p"
                    android:fontFamily="@font/oswald"
                    android:textSize="20dp"
                    android:layout_marginTop="5dp"
                    />
            </LinearLayout>
            <LinearLayout
                android:id="@+id/alertdialogueplaylocalbox4"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                >
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="30dp">

                    <View
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_centerHorizontal="true"
                        android:layout_alignParentBottom="true"
                        android:background="@drawable/checl_box"
                        android:backgroundTint="@color/gree1"
                        />
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/alertdialogueplaylocalcheck4"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/check_icon"
                        android:layout_marginBottom="5dp"
                        android:layout_marginLeft="8dp"
                        app:tint="@color/white"
                        />
                </RelativeLayout>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:textColor="@color/white"
                    android:gravity="center"
                    android:text="4p"
                    android:fontFamily="@font/oswald"
                    android:textSize="20dp"
                    android:layout_marginTop="5dp"
                    />
            </LinearLayout>
        </LinearLayout>
        <TextView
            android:id="@+id/localgamemenuplayercountview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:text="4 player game"
            android:gravity="center"
            android:fontFamily="@font/oswald"
            android:layout_marginTop="20dp"
            android:textSize="20dp"
            />
        <Button
            android:id="@+id/localgamemenustartbutton"
            android:layout_width="180dp"
            android:layout_height="70dp"
            android:layout_marginTop="10dp"
            android:src="@drawable/start_button"
            android:adjustViewBounds="true"
            android:scaleType="centerInside"
            android:background="@drawable/button_backgrond"
            android:text="Start"
            android:fontFamily="@font/mama_bear"
            android:textSize="25dp"
            android:textColor="@color/white"
            />
    </LinearLayout>
    <TextView
        android:layout_width="190dp"
        android:layout_height="40dp"
        android:background="@drawable/play_local_written"
        android:layout_centerHorizontal="true"
        android:backgroundTint="@color/yellow3"
        android:text="PLay Local"
        android:gravity="center"
        android:textColor="@color/purple3"
        android:textSize="20dp"
        android:textStyle="bold"
        android:fontFamily="@font/oswald"
        />
</RelativeLayout>