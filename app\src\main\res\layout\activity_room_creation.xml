<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.first_player_games.OnlineGame.Lobby.RoomCreationActivity">

    <ImageView
        android:id="@+id/img1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        android:src="@drawable/online_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RelativeLayout
        android:id="@+id/header"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/back_btn"
            android:layout_width="60dp"
            android:layout_height="50dp"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/arrow" />

        <ImageView
            android:layout_width="60dp"
            android:layout_height="50dp"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:layout_toLeftOf="@+id/speaker"
            android:background="@drawable/music" />

        <ImageView
            android:id="@+id/speaker"
            android:layout_width="60dp"
            android:layout_height="50dp"
            android:layout_alignParentRight="true"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp"
            android:background="@drawable/speaker" />
    </RelativeLayout>

    <TextView
        android:id="@+id/heading"
        android:layout_width="200dp"
        android:layout_height="25dp"
        android:layout_below="@+id/header"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        android:background="@drawable/roomcreation"
        android:fontFamily="@font/mama_bear"
        android:text=""
        android:textColor="@color/white"
        android:textSize="25dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!--    <com.google.android.gms.ads.AdView-->
    <!--        android:id="@+id/adView"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_alignParentTop="true"-->
    <!--        android:layout_centerHorizontal="true"-->
    <!--        app:adSize="BANNER"-->
    <!--        app:adUnitId="@string/banner_ad_unit_id"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="parent" />-->

    <ImageView
        android:id="@+id/ludo_img"
        android:layout_width="170dp"
        android:layout_height="140dp"
        android:layout_below="@+id/heading"
        android:layout_centerInParent="true"
        android:src="@drawable/ludo_img"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@+id/heading"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/ludo_play_online"
        android:layout_width="180dp"
        android:layout_height="40dp"
        android:layout_below="@+id/ludo_img"
        android:layout_centerInParent="true"
        android:layout_marginTop="5dp"
        android:background="@drawable/ludo_play_img"
        android:text=""
        android:textColor="@color/white"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/roomcreationroomid"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <RelativeLayout
        android:id="@+id/lnr_timer"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:layout_below="@+id/roomcreationroomid"
        android:layout_centerHorizontal="true"
        android:background="@drawable/timer">
        <!--    <ImageView-->
        <!--        android:id="@+id/timer"-->
        <!--        android:layout_width="wrap_content"-->
        <!--        android:layout_height="50dp"-->
        <!--        android:layout_centerInParent="true"-->
        <!--        android:paddingVertical="5dp"-->
        <!--        android:paddingRight="5dp"-->
        <!--        android:scaleType="fitCenter"-->
        <!--        android:src="@drawable/timer"-->
        <!--        android:visibility="visible"-->
        <!--        app:layout_constraintEnd_toEndOf="parent"-->
        <!--        app:layout_constraintStart_toStartOf="parent"-->
        <!--        app:layout_constraintTop_toBottomOf="@+id/roomcreationroomid" />-->
        <TextView
            android:id="@+id/timer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="50dp"
            android:text="00:50"
            android:textColor="@color/white"
            android:textSize="16dp"
            android:textStyle="bold" />
    </RelativeLayout>

    <ImageView
        android:id="@+id/img_dice"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:layout_below="@+id/lnr_timer"
        android:layout_centerInParent="true"
        android:paddingVertical="5dp"
        android:paddingRight="5dp"
        android:scaleType="fitCenter"
        android:src="@drawable/searching"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/roomcreationroomid" />

    <TextView
        android:id="@+id/roomcreationroomid"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/roomcreationjoinedplayersview"
        android:layout_centerInParent="true"
        android:text="Please wait"
        android:textColor="@color/white"
        android:textSize="25dp"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/roomcreationjoinedplayersview"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/heading"
        app:layout_constraintVertical_bias="0.4" />

    <ImageButton
        android:id="@+id/imageButton3"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/rounded_shape"
        android:backgroundTint="@color/yellow3"
        android:paddingVertical="5dp"
        android:paddingRight="5dp"
        android:scaleType="fitCenter"
        android:src="@drawable/icon_share"
        android:tint="@color/purple"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/roomcreationroomid" />

    <TextView
        android:id="@+id/diamonds_amount_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/mama_bear"
        android:text="Winner gets 10 Diamonds"
        android:textColor="@color/white"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@+id/roomcreationjoinedplayersview"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/img1"
        app:layout_constraintTop_toBottomOf="@+id/imageButton3" />

    <RelativeLayout
        android:id="@+id/rlt_total"
        android:layout_width="300dp"
        android:layout_height="40dp"
        android:layout_below="@+id/ludo_play_online"
        android:layout_centerInParent="true"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center_horizontal"
                android:hapticFeedbackEnabled="true"
                android:text="Quick > Entry Amount : "
                android:textColor="#13F31C"
                android:textSize="@dimen/dimen_15sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvamount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="3dp"
                android:gravity="center"
                android:hapticFeedbackEnabled="true"
                android:text="50"
                android:textColor="#13F31C"
                android:textSize="@dimen/dimen_15sp"
                android:textStyle="bold" />
        </LinearLayout>

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/roomcreationjoinedplayersview"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_below="@+id/rlt_total"
        android:layout_marginStart="10dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="20dp"
        android:orientation="vertical"
        android:paddingHorizontal="30dp"
        android:paddingTop="20dp"
        app:layout_constraintBottom_toTopOf="@+id/button">

        <!--        <TextView-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_marginBottom="10dp"-->
        <!--            android:fontFamily="@font/mama_bear"-->
        <!--            android:text="Players Joined"-->
        <!--            android:visibility="gone"-->
        <!--            android:textColor="@color/yellow3" />-->

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/roomcreation_player_1"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:orientation="vertical"
                android:visibility="visible">

                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/avator_one" />

                <TextView
                    android:id="@+id/roomcreation_player_name_1"
                    android:layout_width="wrap_content"
                    android:layout_height="23dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/name_bg"
                    android:gravity="center"
                    android:text=""
                    android:textColor="@color/white"
                    android:textStyle="bold" />
            </LinearLayout>

            <ImageView
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:layout_gravity="center_vertical"
                android:layout_marginBottom="10dp"
                android:src="@drawable/vs" />

            <LinearLayout
                android:id="@+id/roomcreation_player_2"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:orientation="vertical"
                android:visibility="invisible">

                <View
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/avators_two" />

                <TextView
                    android:id="@+id/roomcreation_player_name_2"
                    android:layout_width="wrap_content"
                    android:layout_height="23dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/name_bg"
                    android:gravity="center"
                    android:text=""
                    android:textColor="@color/white"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/roomcreation_player_3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:visibility="invisible">

            <View
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:background="@drawable/checl_box"
                android:backgroundTint="@color/yellow1" />

            <TextView
                android:id="@+id/roomcreation_player_name_3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="Player Name"
                android:textColor="@color/white"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/roomcreation_player_4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="invisible">

            <View
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:background="@drawable/checl_box"
                android:backgroundTint="@color/gree1" />

            <TextView
                android:id="@+id/roomcreation_player_name_4"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="Player Name"
                android:textColor="@color/white"
                android:textStyle="bold" />
        </LinearLayout>

    </LinearLayout>

    <Button
        android:id="@+id/button"
        android:layout_width="260dp"
        android:layout_height="70dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/button_background_long"
        android:fontFamily="@font/mama_bear"
        android:onClick="gameStartButton"
        android:text="Start"
        android:textColor="@color/white"
        android:textSize="20dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</RelativeLayout>