<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="40dp"
        android:paddingTop="40dp"
        android:paddingBottom="20dp"
        android:gravity="center"
        android:background="@drawable/alert_dialogue_background_3"
        android:layout_marginTop="20dp"
        >

        <TextView
            android:id="@+id/localgamemenuplayercountview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:text="Do you want to exit the Game?"
            android:gravity="center"
            android:fontFamily="@font/oswald"
            android:textSize="20dp"
            />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="70dp"
            android:layout_marginTop="10dp"
            android:layout_marginHorizontal="20dp"
            >
            <Button
                android:id="@+id/alert_exit_yes"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="10dp"
                android:src="@drawable/start_button"
                android:adjustViewBounds="true"
                android:scaleType="centerInside"
                android:background="@drawable/button_background_red"
                android:text="yes"
                android:fontFamily="@font/mama_bear"
                android:textSize="25dp"
                android:textColor="@color/white"
                android:layout_weight="1"
                android:layout_marginRight="20dp"
                />
            <Button
                android:id="@+id/alert_exit_no"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="10dp"
                android:src="@drawable/start_button"
                android:adjustViewBounds="true"
                android:scaleType="centerInside"
                android:background="@drawable/button_background_green"
                android:text="no"
                android:fontFamily="@font/mama_bear"
                android:textSize="25dp"
                android:textColor="@color/white"
                android:layout_weight="1"
                android:layout_marginLeft="20dp"
                />
        </LinearLayout>

    </LinearLayout>
    <TextView
        android:layout_width="190dp"
        android:layout_height="40dp"
        android:background="@drawable/play_local_written"
        android:layout_centerHorizontal="true"
        android:backgroundTint="@color/yellow3"
        android:text="Alert !"
        android:gravity="center"
        android:textColor="@color/purple3"
        android:textSize="20dp"
        android:textStyle="bold"
        android:fontFamily="@font/oswald"
        android:elegantTextHeight="true"
        />
</RelativeLayout>