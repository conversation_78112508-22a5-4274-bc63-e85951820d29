<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="deploymentTargetSelector">
    <selectionStates>
      <SelectionState runConfigName="app">
        <option name="selectionMode" value="DROPDOWN" />
        <DropdownSelection timestamp="2025-07-11T13:30:14.595557200Z">
          <Target type="DEFAULT_BOOT">
            <handle>
              <DeviceId pluginId="PhysicalDevice" identifier="serial=QG9PVO7PGIHQPFLF" />
            </handle>
          </Target>
        </DropdownSelection>
        <DialogSelection>
          <targets>
            <Target type="DEFAULT_BOOT">
              <handle>
                <DeviceId pluginId="LocalEmulator" identifier="path=C:\Users\<USER>\.android\avd\Pixel_6_Pro_API_33.avd" />
              </handle>
            </Target>
          </targets>
        </DialogSelection>
      </SelectionState>
      <SelectionState runConfigName="ludogame">
        <option name="selectionMode" value="DROPDOWN" />
      </SelectionState>
    </selectionStates>
  </component>
</project>